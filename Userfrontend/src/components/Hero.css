.hero {
    height: 90vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    padding: 2rem;
    background-image: url('https://images.unsplash.com/photo-1558981403-c5f9899a28bc?q=80&w=2070&auto=format&fit=crop');
    background-size: cover;
    background-position: center;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
}

.hero h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin: 0;
    margin-bottom: 1rem;
}

.hero p {
    font-size: 1.2rem;
    color: var(--color-text-muted);
    margin: 1rem 0 2.5rem 0;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-search-form {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    background-color: rgba(31, 41, 55, 0.9);
    padding: 1.5rem;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.hero-search-form select,
.hero-search-form input,
.hero-search-form button {
    flex: 1;
    min-width: 200px;
    border: none;
    padding: 1rem;
    border-radius: 8px;
    font-size: 1rem;
}

.hero-search-form select,
.hero-search-form input {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-search-form select:focus,
.hero-search-form input:focus {
    border-color: var(--color-primary);
    outline: none;
}

.hero-search-form button {
    background-color: var(--color-primary);
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.hero-search-form button:hover {
    background-color: var(--color-primary-dull);
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .hero {
        height: 80vh;
        padding: 1rem;
    }
    
    .hero h1 {
        font-size: 2.5rem;
    }
    
    .hero p {
        font-size: 1rem;
    }
    
    .hero-search-form {
        flex-direction: column;
        padding: 1rem;
    }
    
    .hero-search-form select,
    .hero-search-form input,
    .hero-search-form button {
        min-width: auto;
        width: 100%;
    }
}
