.bike-card {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    background-color: var(--color-dark-surface);
    border: 1px solid var(--color-border);
}

.bike-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.bike-card-image-wrapper {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.bike-card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.bike-card:hover .bike-card-image {
    transform: scale(1.05);
}

.availability-tag {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background-color: rgba(255, 87, 34, 0.9);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    backdrop-filter: blur(5px);
}

.price-tag {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 8px;
}

.price-tag .price {
    font-weight: 600;
    font-size: 1.1rem;
}

.price-tag .period {
    font-size: 0.8rem;
    color: var(--color-text-muted);
}

.bike-card-content {
    padding: 1.25rem;
}

.bike-card-header {
    margin-bottom: 1rem;
}

.bike-card-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    color: var(--color-text-light);
    margin-bottom: 0.5rem;
}

.bike-card-subtitle {
    font-size: 0.9rem;
    color: var(--color-text-muted);
    margin: 0;
}

@media (max-width: 768px) {
    .bike-card-image-wrapper {
        height: 180px;
    }
    
    .bike-card-content {
        padding: 1rem;
    }
    
    .bike-card-title {
        font-size: 1.1rem;
    }
    
    .bike-card-subtitle {
        font-size: 0.85rem;
    }
}
