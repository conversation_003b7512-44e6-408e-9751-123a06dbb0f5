.footer {
    background-color: var(--color-dark-surface);
    border-top: 1px solid var(--color-border);
    margin-top: 4rem;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 3rem 5% 2rem;
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
    gap: 2rem;
}

.footer-brand {
    max-width: 300px;
}

.footer-logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--color-primary);
    text-decoration: none;
    letter-spacing: 1px;
    margin-bottom: 1rem;
    display: block;
}

.footer-description {
    color: var(--color-text-muted);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--color-text-muted);
    transition: all 0.3s ease;
    text-decoration: none;
}

.social-link:hover {
    background-color: var(--color-primary);
    color: white;
    transform: translateY(-2px);
}

.footer-section h3 {
    color: var(--color-text-light);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-section ul li {
    margin-bottom: 0.75rem;
}

.footer-section ul li a {
    color: var(--color-text-muted);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--color-primary);
}

.footer-contact .contact-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--color-text-muted);
    font-size: 0.9rem;
}

.contact-icon {
    font-size: 1rem;
}

.footer-bottom {
    border-top: 1px solid var(--color-border);
    background-color: rgba(0, 0, 0, 0.2);
}

.footer-bottom-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1.5rem 5%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-bottom p {
    color: var(--color-text-muted);
    margin: 0;
    font-size: 0.9rem;
}

.footer-bottom-links {
    display: flex;
    gap: 2rem;
}

.footer-bottom-links a {
    color: var(--color-text-muted);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-bottom-links a:hover {
    color: var(--color-primary);
}

@media (max-width: 1024px) {
    .footer-content {
        grid-template-columns: 1fr 1fr 1fr;
        gap: 2rem;
    }
    
    .footer-brand {
        grid-column: 1 / -1;
        max-width: none;
        margin-bottom: 1rem;
    }
}

@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr 1fr;
        padding: 2rem 5% 1.5rem;
        gap: 1.5rem;
    }
    
    .footer-contact {
        grid-column: 1 / -1;
    }
    
    .footer-bottom-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
        padding: 1rem 5%;
    }
    
    .footer-bottom-links {
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .footer-content {
        grid-template-columns: 1fr;
        padding: 1.5rem 5% 1rem;
    }
    
    .footer-section {
        text-align: center;
    }
    
    .social-links {
        justify-content: center;
    }
    
    .contact-item {
        justify-content: center;
    }
}
