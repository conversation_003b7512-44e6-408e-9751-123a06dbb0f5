.login-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.login-modal {
    background-color: var(--color-dark-surface);
    border-radius: 16px;
    padding: 0;
    width: 100%;
    max-width: 450px;
    margin: 1rem;
    border: 1px solid var(--color-border);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    overflow: hidden;
}

.login-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid var(--color-border);
}

.login-header h2 {
    color: var(--color-text-light);
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
}

.close-button {
    background: none;
    border: none;
    color: var(--color-text-muted);
    font-size: 2rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--color-text-light);
}

.login-form {
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    color: var(--color-text-light);
    font-weight: 500;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-group input {
    width: 100%;
    padding: 1rem;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    background-color: var(--color-dark-bg);
    color: var(--color-text-light);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(255, 87, 34, 0.1);
}

.form-group input::placeholder {
    color: var(--color-text-muted);
}

.password-hint {
    display: block;
    color: var(--color-text-muted);
    font-size: 0.8rem;
    margin-top: 0.5rem;
}

.submit-button {
    width: 100%;
    background-color: var(--color-primary);
    color: white;
    padding: 1rem;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 0.5rem;
}

.submit-button:hover:not(:disabled) {
    background-color: var(--color-primary-dull);
    transform: translateY(-2px);
}

.submit-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.login-footer {
    padding: 1.5rem 2rem 2rem;
    text-align: center;
    border-top: 1px solid var(--color-border);
    background-color: rgba(255, 255, 255, 0.02);
}

.login-footer p {
    color: var(--color-text-muted);
    margin: 0;
    font-size: 0.9rem;
}

.toggle-button {
    background: none;
    border: none;
    color: var(--color-primary);
    font-weight: 600;
    cursor: pointer;
    padding: 0;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.toggle-button:hover {
    color: var(--color-primary-dull);
    text-decoration: underline;
}

/* Email Verification Notice */
.verification-notice {
    background-color: rgba(255, 87, 34, 0.1);
    border: 1px solid var(--color-primary);
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    text-align: center;
}

.verification-notice p {
    color: var(--color-text-light);
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.resend-button {
    background-color: transparent;
    border: 1px solid var(--color-primary);
    color: var(--color-primary);
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.resend-button:hover:not(:disabled) {
    background-color: var(--color-primary);
    color: white;
}

.resend-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}



/* Forgot Password Link */
.forgot-password-link {
    text-align: center;
    margin-top: 1rem;
}

.link-button {
    background: none;
    border: none;
    color: var(--color-primary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: color 0.3s ease;
}

.link-button:hover {
    color: var(--color-primary-dull);
    text-decoration: underline;
}

@media (max-width: 480px) {
    .login-modal {
        margin: 0.5rem;
        max-width: none;
    }
    
    .login-header {
        padding: 1.5rem 1.5rem 1rem;
    }
    
    .login-header h2 {
        font-size: 1.5rem;
    }
    
    .login-form {
        padding: 1.5rem;
    }
    
    .login-footer {
        padding: 1rem 1.5rem 1.5rem;
    }
}
