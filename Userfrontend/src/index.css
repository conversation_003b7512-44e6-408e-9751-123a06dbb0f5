
@import url('https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700&display=swap');

:root {
  --color-primary: #FF5722;
  --color-primary-dull: #E64A19;
  --color-dark-bg: #111827;
  --color-dark-surface: #1F2937;
  --color-text-light: #F9FAFB;
  --color-text-dark: #374151;
  --color-text-muted: #9CA3AF;
  --color-border: #374151;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: "Outfit", sans-serif;
  background-color: var(--color-dark-bg);
  color: var(--color-text-light);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
}

a {
  text-decoration: none;
  color: inherit;
  transition: color 0.3s ease;
}

a:hover {
    color: var(--color-primary);
}

button {
  cursor: pointer;
  border: none;
  font-family: "Outfit", sans-serif;
  transition: background-color 0.3s ease, transform 0.2s ease;
}

button:hover {
  transform: translateY(-1px);
}

input, select, textarea {
    font-family: "Outfit", sans-serif;
    width: 100%;
    box-sizing: border-box;
    padding: 10px 12px;
    border-radius: 5px;
    border: 1px solid var(--color-border);
    background-color: var(--color-dark-surface);
    color: var(--color-text-light);
    outline: none;
    transition: border-color 0.3s ease;
}

input:focus, select:focus, textarea:focus {
    border-color: var(--color-primary);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.text-center {
  text-align: center;
}

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
