.forgot-password-page {
    min-height: 100vh;
    background-color: var(--color-dark-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.forgot-password-container {
    width: 100%;
    max-width: 500px;
}

.forgot-password-card {
    background-color: var(--color-dark-surface);
    border-radius: 16px;
    padding: 3rem 2rem;
    border: 1px solid var(--color-border);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

.forgot-password-header {
    text-align: center;
    margin-bottom: 2rem;
}

.forgot-password-header h2 {
    color: var(--color-text-light);
    margin-bottom: 1rem;
    font-size: 1.8rem;
}

.forgot-password-header p {
    color: var(--color-text-muted);
    line-height: 1.6;
    margin: 0;
}

.forgot-password-form {
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    color: var(--color-text-light);
    font-weight: 500;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-group input {
    width: 100%;
    padding: 1rem;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    background-color: var(--color-dark-bg);
    color: var(--color-text-light);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(255, 87, 34, 0.1);
}

.form-group input::placeholder {
    color: var(--color-text-muted);
}

.btn {
    width: 100%;
    padding: 1rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--color-primary);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--color-primary-dull);
    transform: translateY(-2px);
}

.btn-primary:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-secondary {
    background-color: transparent;
    color: var(--color-text-muted);
    border: 1px solid var(--color-border);
    width: auto;
    padding: 0.75rem 1.5rem;
    margin-right: 1rem;
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--color-text-light);
}

.forgot-password-footer {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--color-border);
}

.forgot-password-footer p {
    color: var(--color-text-muted);
    margin: 0;
    font-size: 0.9rem;
}

.link-button {
    background: none;
    border: none;
    color: var(--color-primary);
    font-weight: 600;
    cursor: pointer;
    padding: 0;
    margin-left: 0.5rem;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.link-button:hover {
    color: var(--color-primary-dull);
    text-decoration: underline;
}

/* Success State Styles */
.success-state {
    text-align: center;
}

.success-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
}

.success-state h2 {
    color: var(--color-text-light);
    margin-bottom: 1rem;
    font-size: 1.8rem;
}

.success-state p {
    color: var(--color-text-muted);
    line-height: 1.6;
    margin-bottom: 2rem;
}

.email-tips {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1.5rem;
    margin: 2rem 0;
    text-align: left;
}

.email-tips h3 {
    color: var(--color-text-light);
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.email-tips ul {
    color: var(--color-text-muted);
    padding-left: 1.5rem;
    line-height: 1.8;
}

.email-tips li {
    margin-bottom: 0.5rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

@media (max-width: 480px) {
    .forgot-password-page {
        padding: 1rem;
    }
    
    .forgot-password-card {
        padding: 2rem 1.5rem;
    }
    
    .forgot-password-header h2 {
        font-size: 1.5rem;
    }
    
    .success-icon {
        font-size: 3rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .btn-secondary {
        width: 100%;
        margin-right: 0;
        margin-bottom: 1rem;
    }
}
