.auth-success-page {
    min-height: 100vh;
    background-color: var(--color-dark-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.auth-success-container {
    width: 100%;
    max-width: 500px;
}

.auth-success-card {
    background-color: var(--color-dark-surface);
    border-radius: 16px;
    padding: 3rem 2rem;
    border: 1px solid var(--color-border);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    text-align: center;
}

.success-animation {
    position: relative;
    margin-bottom: 2rem;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.success-icon {
    font-size: 4rem;
    animation: bounceIn 0.8s ease-out;
}

.google-icon {
    font-size: 2rem;
    position: absolute;
    top: 0;
    right: 50%;
    transform: translateX(50%);
    animation: slideIn 1s ease-out 0.5s both;
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes slideIn {
    0% {
        transform: translateX(100px);
        opacity: 0;
    }
    100% {
        transform: translateX(50%);
        opacity: 1;
    }
}

.auth-success-card h2 {
    color: var(--color-text-light);
    margin-bottom: 1rem;
    font-size: 1.8rem;
}

.auth-success-card p {
    color: var(--color-text-muted);
    line-height: 1.6;
    margin-bottom: 2rem;
}

.loading-indicator {
    margin: 2rem 0;
}

.loading-dots {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

.loading-dots span {
    width: 8px;
    height: 8px;
    background-color: var(--color-primary);
    border-radius: 50%;
    animation: pulse 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes pulse {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--color-primary);
    color: white;
}

.btn-primary:hover {
    background-color: var(--color-primary-dull);
    transform: translateY(-2px);
}

@media (max-width: 480px) {
    .auth-success-page {
        padding: 1rem;
    }
    
    .auth-success-card {
        padding: 2rem 1.5rem;
    }
    
    .auth-success-card h2 {
        font-size: 1.5rem;
    }
    
    .success-icon {
        font-size: 3rem;
    }
    
    .google-icon {
        font-size: 1.5rem;
    }
}
