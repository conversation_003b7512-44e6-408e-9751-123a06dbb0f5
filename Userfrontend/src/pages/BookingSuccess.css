.booking-success-page {
    min-height: 100vh;
    background-color: var(--color-dark-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.booking-success-container {
    width: 100%;
    max-width: 600px;
}

.booking-success-card {
    background-color: var(--color-dark-surface);
    border-radius: 16px;
    padding: 3rem 2rem;
    border: 1px solid var(--color-border);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    text-align: center;
}

.verification-status h2 {
    color: var(--color-text-light);
    margin-bottom: 1rem;
    font-size: 1.8rem;
}

.verification-status p {
    color: var(--color-text-muted);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid var(--color-border);
    border-top: 4px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 2rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.success-icon,
.error-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
}

.booking-details {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 2rem;
    margin: 2rem 0;
    text-align: left;
}

.booking-details h3 {
    color: var(--color-text-light);
    margin-bottom: 1.5rem;
    text-align: center;
    font-size: 1.2rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--color-border);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row span:first-child {
    color: var(--color-text-muted);
    font-weight: 500;
}

.detail-row span:last-child {
    color: var(--color-text-light);
    font-weight: 600;
}

.status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status.pending {
    background-color: rgba(255, 193, 7, 0.2);
    color: #ffc107;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background-color: var(--color-primary);
    color: white;
}

.btn-primary:hover {
    background-color: var(--color-primary-dull);
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: transparent;
    color: var(--color-text-muted);
    border: 1px solid var(--color-border);
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--color-text-light);
}

@media (max-width: 480px) {
    .booking-success-page {
        padding: 1rem;
    }
    
    .booking-success-card {
        padding: 2rem 1.5rem;
    }
    
    .verification-status h2 {
        font-size: 1.5rem;
    }
    
    .success-icon,
    .error-icon {
        font-size: 3rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
    
    .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}
