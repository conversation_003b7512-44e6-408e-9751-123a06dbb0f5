.bike-details {
    min-height: 100vh;
    background-color: var(--color-dark-bg);
    padding: 2rem 5%;
}

.bike-details-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 50vh;
    gap: 1rem;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--color-border);
    border-top: 4px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.bike-details-container {
    max-width: 1200px;
    margin: 0 auto;
}

.bike-details-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.bike-details-image {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.bike-details-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.availability-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background-color: var(--color-primary);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}

.bike-details-info {
    background-color: var(--color-dark-surface);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--color-border);
}

.bike-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    color: var(--color-text-light);
    margin-bottom: 0.5rem;
}

.bike-category {
    color: var(--color-text-muted);
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.bike-price {
    margin-bottom: 2rem;
}

.bike-price .price {
    font-size: 2rem;
    font-weight: 700;
    color: var(--color-primary);
}

.bike-price .period {
    font-size: 1.2rem;
    color: var(--color-text-muted);
}

.bike-specs,
.bike-description {
    margin-bottom: 2rem;
}

.bike-specs h3,
.bike-description h3 {
    color: var(--color-text-light);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.specs-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.spec-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.spec-label {
    color: var(--color-text-muted);
    font-weight: 500;
}

.spec-value {
    color: var(--color-text-light);
    font-weight: 600;
}

.bike-description p {
    color: var(--color-text-muted);
    line-height: 1.6;
    margin: 0;
}

.booking-form {
    background-color: rgba(255, 87, 34, 0.1);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid var(--color-primary);
}

.booking-form h3 {
    color: var(--color-primary);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    color: var(--color-text-light);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input {
    padding: 0.75rem;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    background-color: var(--color-dark-bg);
    color: var(--color-text-light);
}

.booking-summary {
    background-color: rgba(255, 255, 255, 0.05);
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.booking-summary p {
    margin: 0;
    color: var(--color-text-light);
}

.total-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--color-primary) !important;
}

.book-button {
    width: 100%;
    background-color: var(--color-primary);
    color: white;
    padding: 1rem;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.book-button:hover:not(:disabled) {
    background-color: var(--color-primary-dull);
    transform: translateY(-2px);
}

.book-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.unavailable-notice {
    background-color: rgba(239, 68, 68, 0.1);
    border: 1px solid #ef4444;
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
}

.unavailable-notice p {
    color: #ef4444;
    font-weight: 600;
    margin: 0;
}

@media (max-width: 768px) {
    .bike-details {
        padding: 1rem;
    }
    
    .bike-details-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .bike-details-image img {
        height: 250px;
    }
    
    .bike-details-info {
        padding: 1.5rem;
    }
    
    .bike-header h1 {
        font-size: 2rem;
    }
    
    .specs-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
}
