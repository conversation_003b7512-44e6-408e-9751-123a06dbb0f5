.my-bookings {
    min-height: 100vh;
    background-color: var(--color-dark-bg);
    padding: 2rem 5%;
}

.my-bookings-container {
    max-width: 1200px;
    margin: 0 auto;
}

.my-bookings-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 50vh;
    gap: 1rem;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--color-border);
    border-top: 4px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.bookings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.booking-card {
    background-color: var(--color-dark-surface);
    border-radius: 12px;
    border: 1px solid var(--color-border);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: fit-content;
}

.booking-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.booking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--color-border);
    background-color: rgba(255, 255, 255, 0.02);
}

.booking-id {
    font-family: monospace;
    color: var(--color-text-muted);
    font-size: 0.9rem;
}

.booking-status {
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending {
    background-color: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
    border: 1px solid #fbbf24;
}

.status-confirmed {
    background-color: rgba(255, 87, 34, 0.2);
    color: var(--color-primary);
    border: 1px solid var(--color-primary);
}

.status-completed {
    background-color: rgba(16, 185, 129, 0.2);
    color: #10b981;
    border: 1px solid #10b981;
}

.status-cancelled {
    background-color: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid #ef4444;
}

.booking-content {
    padding: 1.5rem;
    display: flex;
    gap: 1.5rem;
}

.bike-info {
    display: flex;
    gap: 1rem;
    flex: 0 0 200px;
    width: 300px;
    height: 100px;
}

.bike-thumbnail {
    width: 70px;
    height: 70px;
    object-fit: cover;
    border-radius: 8px;
    flex-shrink: 0;
}

.bike-details {
    flex: 1;
    min-width: 0;
}

.bike-details h3 {
    color: var(--color-text-light);
    margin: 0;
    font-size: 1rem;
    margin-bottom: 0.25rem;
    line-height: 1.2;
}

.bike-details p {
    color: var(--color-text-muted);
    margin: 0;
    font-size: 0.85rem;
    line-height: 1.3;
    margin-bottom: 0.2rem;
}

.bike-location {
    color: var(--color-primary) !important;
    font-size: 0.8rem !important;
    margin-top: 0.25rem !important;
}

.booking-details {
    display: flex;
    flex-direction: column;
    gap: 0.6rem;
    flex: 1;
    min-width: 0;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.3rem 0;
}

.detail-row .label {
    color: var(--color-text-muted);
    font-size: 0.85rem;
    font-weight: 500;
}

.detail-row .value {
    color: var(--color-text-light);
    font-weight: 600;
    font-size: 0.85rem;
    text-align: right;
}

.detail-row .value.price {
    color: var(--color-primary);
    font-weight: 700;
    font-size: 1rem;
}

.no-bookings {
    text-align: center;
    padding: 4rem 2rem;
    background-color: var(--color-dark-surface);
    border-radius: 12px;
    border: 1px solid var(--color-border);
    margin-top: 2rem;
}

.no-bookings-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.no-bookings h3 {
    color: var(--color-text-light);
    font-size: 1.8rem;
    margin-bottom: 1rem;
}

.no-bookings p {
    color: var(--color-text-muted);
    font-size: 1.1rem;
    margin-bottom: 2rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.browse-bikes-btn {
    background-color: var(--color-primary);
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.browse-bikes-btn:hover {
    background-color: var(--color-primary-dull);
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .my-bookings {
        padding: 1rem;
    }
    
    .bookings-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .booking-content {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .booking-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
        text-align: center;
    }

    .bike-info {
        flex-direction: column;
        text-align: center;
        flex: none;
        border-bottom: 1px solid var(--color-border);
        padding-bottom: 1rem;
    }
    
    .bike-thumbnail {
        align-self: center;
        width: 100px;
        height: 75px;
    }
    
    .detail-row {
        flex-direction: column;
        gap: 0.25rem;
        text-align: center;
    }
    
    .no-bookings {
        padding: 3rem 1rem;
    }
    
    .no-bookings h3 {
        font-size: 1.5rem;
    }
}
