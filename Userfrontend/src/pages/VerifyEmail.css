.verify-email-page {
    min-height: 100vh;
    background-color: var(--color-dark-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.verify-email-container {
    width: 100%;
    max-width: 500px;
}

.verify-email-card {
    background-color: var(--color-dark-surface);
    border-radius: 16px;
    padding: 3rem 2rem;
    border: 1px solid var(--color-border);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    text-align: center;
}

.verification-status h2 {
    color: var(--color-text-light);
    margin-bottom: 1rem;
    font-size: 1.8rem;
}

.verification-status p {
    color: var(--color-text-muted);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid var(--color-border);
    border-top: 4px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 2rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.success-icon,
.error-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
}

.redirect-info {
    background-color: rgba(255, 87, 34, 0.1);
    border: 1px solid var(--color-primary);
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.redirect-info p {
    color: var(--color-text-muted);
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.resend-section {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 2rem;
    margin: 2rem 0;
}

.resend-section h3 {
    color: var(--color-text-light);
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.resend-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.resend-form input {
    padding: 1rem;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    background-color: var(--color-dark-bg);
    color: var(--color-text-light);
    font-size: 1rem;
}

.resend-form input:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(255, 87, 34, 0.1);
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--color-primary);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--color-primary-dull);
    transform: translateY(-2px);
}

.btn-primary:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-secondary {
    background-color: transparent;
    color: var(--color-text-muted);
    border: 1px solid var(--color-border);
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--color-text-light);
}

.back-to-home {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--color-border);
}

@media (max-width: 480px) {
    .verify-email-page {
        padding: 1rem;
    }
    
    .verify-email-card {
        padding: 2rem 1.5rem;
    }
    
    .verification-status h2 {
        font-size: 1.5rem;
    }
    
    .success-icon,
    .error-icon {
        font-size: 3rem;
    }
    
    .resend-section {
        padding: 1.5rem;
    }
}
