.reset-password-page {
    min-height: 100vh;
    background-color: var(--color-dark-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.reset-password-container {
    width: 100%;
    max-width: 500px;
}

.reset-password-card {
    background-color: var(--color-dark-surface);
    border-radius: 16px;
    padding: 3rem 2rem;
    border: 1px solid var(--color-border);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

.reset-password-header {
    text-align: center;
    margin-bottom: 2rem;
}

.reset-password-header h2 {
    color: var(--color-text-light);
    margin-bottom: 1rem;
    font-size: 1.8rem;
}

.reset-password-header p {
    color: var(--color-text-muted);
    line-height: 1.6;
    margin: 0;
}

.reset-password-form {
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    color: var(--color-text-light);
    font-weight: 500;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-group input {
    width: 100%;
    padding: 1rem;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    background-color: var(--color-dark-bg);
    color: var(--color-text-light);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(255, 87, 34, 0.1);
}

.form-group input::placeholder {
    color: var(--color-text-muted);
}

.password-hint {
    display: block;
    color: var(--color-text-muted);
    font-size: 0.8rem;
    margin-top: 0.5rem;
}

.password-strength {
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.strength-indicators {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.strength-indicator {
    color: var(--color-text-muted);
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.strength-indicator.valid {
    color: #10b981;
}

.btn {
    width: 100%;
    padding: 1rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--color-primary);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--color-primary-dull);
    transform: translateY(-2px);
}

.btn-primary:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.reset-password-footer {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--color-border);
}

.reset-password-footer p {
    color: var(--color-text-muted);
    margin: 0;
    font-size: 0.9rem;
}

.link-button {
    background: none;
    border: none;
    color: var(--color-primary);
    font-weight: 600;
    cursor: pointer;
    padding: 0;
    margin-left: 0.5rem;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.link-button:hover {
    color: var(--color-primary-dull);
    text-decoration: underline;
}

@media (max-width: 480px) {
    .reset-password-page {
        padding: 1rem;
    }
    
    .reset-password-card {
        padding: 2rem 1.5rem;
    }
    
    .reset-password-header h2 {
        font-size: 1.5rem;
    }
    
    .strength-indicators {
        gap: 0.75rem;
    }
}
