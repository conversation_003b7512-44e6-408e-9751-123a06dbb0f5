.about-us {
    background-color: var(--color-dark-bg);
    color: var(--color-text-light);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Hero Section */
.about-hero {
    background: linear-gradient(135deg, rgba(255, 87, 34, 0.9), rgba(255, 87, 34, 0.7)),
                url('https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=1200') center/cover;
    height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-content p {
    font-size: 1.3rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Our Story Section */
.our-story {
    padding: 6rem 0;
    background-color: var(--color-dark-surface);
}

.story-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.story-text h2 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: var(--color-primary);
}

.story-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
    color: var(--color-text-muted);
}

.story-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Why Choose Us Section */
.why-choose-us {
    padding: 6rem 0;
    background-color: var(--color-dark-bg);
}

.why-choose-us h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: var(--color-primary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background-color: var(--color-dark-surface);
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    border: 1px solid var(--color-border);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 87, 34, 0.2);
    border-color: var(--color-primary);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--color-text-light);
}

.feature-card p {
    color: var(--color-text-muted);
    line-height: 1.6;
}

/* Our Locations Section */
.our-locations {
    padding: 6rem 0;
    background-color: var(--color-dark-surface);
}

.our-locations h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: var(--color-primary);
}

.locations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.location-card {
    background-color: var(--color-dark-bg);
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    border: 1px solid var(--color-border);
    transition: all 0.3s ease;
}

.location-card:hover {
    transform: translateY(-3px);
    border-color: var(--color-primary);
}

.location-card h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--color-primary);
}

.location-card p {
    color: var(--color-text-muted);
    margin-bottom: 1rem;
}

.bikes-count {
    background-color: var(--color-primary);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

/* Contact Section */
.contact-section {
    padding: 6rem 0;
    background-color: var(--color-dark-bg);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--color-primary);
}

.contact-info p {
    font-size: 1.1rem;
    color: var(--color-text-muted);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.contact-item strong {
    color: var(--color-text-light);
    font-size: 1rem;
}

.contact-item span {
    color: var(--color-text-muted);
    font-size: 0.95rem;
}

/* Contact Form */
.contact-form {
    background-color: var(--color-dark-surface);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--color-border);
}

.contact-form h3 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: var(--color-text-light);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--color-text-light);
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    background-color: var(--color-dark-bg);
    color: var(--color-text-light);
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--color-primary);
}

.submit-btn {
    width: 100%;
    padding: 1rem;
    background-color: var(--color-primary);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.submit-btn:hover:not(:disabled) {
    background-color: var(--color-primary-dull);
    transform: translateY(-2px);
}

.submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .hero-content p {
        font-size: 1.1rem;
    }
    
    .story-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .features-grid,
    .locations-grid {
        grid-template-columns: 1fr;
    }
    
    .our-story,
    .why-choose-us,
    .our-locations,
    .contact-section {
        padding: 3rem 0;
    }
}
