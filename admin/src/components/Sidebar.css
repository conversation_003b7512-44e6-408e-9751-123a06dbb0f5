.sidebar {
    width: 280px;
    background-color: var(--color-dark-surface);
    padding: 2rem 0;
    border-right: 1px solid var(--color-border);
    height: 100vh;
    position: sticky;
    top: 0;
}

.sidebar nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar nav li {
    margin-bottom: 0.5rem;
}

.sidebar nav a {
    display: block;
    padding: 1rem 2rem;
    color: var(--color-text-muted);
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: 0 25px 25px 0;
    margin-right: 1rem;
    font-weight: 500;
}

.sidebar nav a:hover {
    background-color: #2d3748;
    color: var(--color-text-light);
    transform: translateX(5px);
}

.sidebar nav a.active {
    background-color: var(--color-primary);
    color: white;
    font-weight: 600;
    transform: translateX(5px);
    box-shadow: 0 4px 8px rgba(255, 87, 34, 0.3);
}

@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: static;
        padding: 1rem 0;
    }
    
    .sidebar nav {
        display: flex;
        overflow-x: auto;
    }
    
    .sidebar nav ul {
        display: flex;
        gap: 0.5rem;
        padding: 0 1rem;
        min-width: max-content;
    }
    
    .sidebar nav li {
        margin-bottom: 0;
    }
    
    .sidebar nav a {
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        margin-right: 0;
        white-space: nowrap;
        font-size: 0.9rem;
    }
    
    .sidebar nav a:hover,
    .sidebar nav a.active {
        transform: none;
    }
}
