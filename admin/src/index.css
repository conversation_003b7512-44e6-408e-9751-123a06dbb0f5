/* Admin Global Styles */
@import url('https://fonts.googleapis.com/css2?family=Outfit:wght@100..900&display=swap');

:root {
  --color-primary: #FF5722;
  --color-primary-dull: #E64A19;
  --color-dark-bg: #1f2937;
  --color-dark-surface: #374151;
  --color-text-light: #f9fafb;
  --color-text-muted: #9ca3af;
  --color-border: #4b5563;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-danger: #ef4444;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: "Outfit", sans-serif;
  background-color: var(--color-dark-bg);
  color: var(--color-text-light);
  line-height: 1.6;
}

button {
  border: none;
  cursor: pointer;
  font-family: inherit;
  transition: all 0.3s ease;
}

button:hover {
  opacity: 0.9;
}

input, textarea, select {
  font-family: inherit;
  outline: none;
}

a {
  text-decoration: none;
  color: inherit;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
}

.btn-success {
  background-color: var(--color-success);
  color: white;
}

.btn-warning {
  background-color: var(--color-warning);
  color: white;
}

.btn-danger {
  background-color: var(--color-danger);
  color: white;
}

.btn-secondary {
  background-color: var(--color-dark-surface);
  color: var(--color-text-light);
  border: 1px solid var(--color-border);
}

.card {
  background-color: var(--color-dark-surface);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid var(--color-border);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--color-text-muted);
  font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.8rem 1rem;
  border: 1px solid var(--color-border);
  border-radius: 8px;
  background-color: #2d3748;
  color: var(--color-text-light);
  font-size: 1rem;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(255, 87, 34, 0.1);
}

.table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--color-dark-surface);
  border-radius: 8px;
  overflow: hidden;
}

.table th,
.table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid var(--color-border);
}

.table th {
  background-color: #2d3748;
  font-weight: 600;
  color: var(--color-text-light);
}

.table tr:hover {
  background-color: #2d3748;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-active {
  background-color: var(--color-success);
  color: white;
}

.status-inactive {
  background-color: var(--color-danger);
  color: white;
}

.status-pending {
  background-color: var(--color-warning);
  color: white;
}

.grid {
  display: grid;
  gap: 1.5rem;
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.p-4 { padding: 2rem; }

@media (max-width: 768px) {
  .container {
    padding: 0 0.5rem;
  }
  
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }
  
  .table {
    font-size: 0.9rem;
  }
  
  .table th,
  .table td {
    padding: 0.75rem 0.5rem;
  }
}
