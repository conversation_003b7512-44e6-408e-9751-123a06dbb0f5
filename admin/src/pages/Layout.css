.admin-layout {
    min-height: 100vh;
    background-color: var(--color-dark-bg);
}

.admin-content {
    display: flex;
    min-height: calc(100vh - 80px); /* Adjust based on navbar height */
}

.admin-main {
    flex: 1;
    background-color: var(--color-dark-bg);
    overflow-x: auto;
}

.admin-main-content {
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

@media (max-width: 768px) {
    .admin-content {
        flex-direction: column;
        min-height: auto;
    }
    
    .admin-main-content {
        padding: 1rem;
    }
}
