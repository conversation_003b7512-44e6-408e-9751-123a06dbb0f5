.dashboard {
    padding: 0;
}

.dashboard-header {
    margin-bottom: 2rem;
}

.dashboard-header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    color: var(--color-text-light);
}

.dashboard-header p {
    color: var(--color-text-muted);
    font-size: 1.1rem;
    margin: 0;
}

.dashboard-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    gap: 1rem;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--color-border);
    border-top: 4px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background-color: var(--color-dark-surface);
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid var(--color-border);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.stat-icon {
    font-size: 3rem;
    background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dull));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-content h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    color: var(--color-text-light);
}

.stat-content p {
    color: var(--color-text-muted);
    margin: 0;
    font-size: 1rem;
    font-weight: 500;
}

.dashboard-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.dashboard-section {
    background-color: var(--color-dark-surface);
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid var(--color-border);
}

.dashboard-section h2 {
    margin: 0 0 1.5rem 0;
    color: var(--color-text-light);
    font-size: 1.5rem;
}

.bookings-table {
    overflow-x: auto;
}

.no-data {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--color-text-muted);
}

.no-data p {
    font-size: 1.1rem;
    margin: 0;
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.action-btn {
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
}

.action-btn:hover {
    transform: translateX(5px);
}

@media (max-width: 1024px) {
    .dashboard-content {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .dashboard-header h1 {
        font-size: 2rem;
    }
    
    .dashboard-stats {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        padding: 1.5rem;
    }
    
    .stat-icon {
        font-size: 2.5rem;
    }
    
    .stat-content h3 {
        font-size: 2rem;
    }
    
    .dashboard-section {
        padding: 1.5rem;
    }
    
    .quick-actions {
        gap: 0.75rem;
    }
    
    .action-btn {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
}
