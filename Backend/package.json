{"name": "backend", "version": "1.0.0", "description": "", "main": "server.js", "type": "module", "scripts": {"server": "nodemon server.js", "start": "node server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^6.0.0", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.5.0", "express": "^5.1.0", "express-session": "^1.18.1", "imagekit": "^6.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.0", "multer": "^2.0.1", "nodemailer": "^6.10.1", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "stripe": "^18.2.1"}, "devDependencies": {"nodemon": "^3.1.10"}}